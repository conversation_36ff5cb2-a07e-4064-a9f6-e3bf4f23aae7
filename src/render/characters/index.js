import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { SkeletonUtils } from 'three/examples/jsm/Addons.js';
import { globalLoading } from '../../composables/useLoading.js';
import models, { getRandomModelByType } from '../config/index.js';
import { v4 } from 'uuid';
export class CharacterManager {
	constructor(viewEngine) {
		this.model = null;
		this.characters = [];
		this.animationMixers = [];
		this.moveData = [];
		this.viewEngine = viewEngine;
		this.timeIndex = 0;
		// this.animations = [];
		this.modelAnimations = new Map(); // 存储每个模型对应的animations
		this.characterStates = [];
		this.animationActions = [];
		this.animationIndex = 0;
		this.targetPositions = [];
		this.targetRotations = [];
		this.previousPositions = [];
		this.lastUpdateTime = 0;
		this.updateInterval = 500;
		this.lerpFactor = 0.05;
		this.isPlaying = false;
		// 添加贴图管理
		this.textureLoader = new THREE.TextureLoader();

		this.models = new Map();
		this.textures = new Map();
		this.personInfo = [];
		this.characterMaterials = []; // 存储所有人物材质，用于统一管理
		// 添加帧率相关参数，用于时间计算
		this.frameRate = 2; // 每秒2帧

		//0.5一帧
	}

	async loadTexture(textureConfig) {
		return new Promise((resolve, reject) => {
			if (this.textures.has(textureConfig.name)) {
				resolve();
				return;
			}
			this.textureLoader.load(`./textures/${textureConfig.path}`, (texture) => {
				// 设置贴图参数
				texture.wrapS = THREE.RepeatWrapping;
				texture.wrapT = THREE.RepeatWrapping;
				texture.flipY = false; // GLB模型通常需要设置为false

				// 关键：设置正确的颜色空间
				texture.colorSpace = THREE.SRGBColorSpace;

				// 增强贴图的对比度和饱和度
				texture.magFilter = THREE.LinearFilter;
				texture.minFilter = THREE.LinearMipmapLinearFilter;
				texture.generateMipmaps = true;

				this.textures.set(textureConfig.name, texture);
				resolve();
			});
		});
	}

	applyTextureToModel(model, texture) {
		model.traverse((child) => {
			if (child.isMesh && child.material) {
				const material = new THREE.MeshLambertMaterial({
					map: texture,
					// Lambert材质没有roughness和metalness属性，提供更自然的漫反射
					transparent: false,
					opacity: 1.0,
					// 使用标准白色，避免过度提亮
					color: new THREE.Color(1.0, 1.0, 1.0),
				});

				// 重要：设置材质的色调映射参数
				material.toneMapped = true;

				// 应用材质
				child.material = material;

				// 将材质添加到管理列表
				this.characterMaterials.push(material);

				// 启用阴影
				child.castShadow = true;
				child.receiveShadow = true;
			}
		});
	}

	async loadModel(model) {
		return new Promise((resolve, reject) => {
			const loader = new GLTFLoader();
			if (this.models.has(model.name)) {
				resolve();
				return;
			}

			if (import.meta.env.DEV) {
				loader.load(`./glb/${model.name}.glb`, (gltf) => {
					// 存储每个模型自己的animations
					this.modelAnimations.set(model.name, gltf.animations);

					this.models.set(model.name, gltf.scene);
					resolve();
				});
			} else {
				window.Channel.loadGLB(`${model.name}.glb`)
					.then((gltf) => {
						// 存储每个模型自己的animations
						this.modelAnimations.set(model.name, gltf.animations);
						this.models.set(model.name, gltf.scene);
						resolve();
					})
					.catch(() => {
						resolve();
					});
			}
		});
	}

	async loadPersonInfo() {
		return new Promise((resolve, reject) => {
			if (import.meta.env.DEV) {
				fetch('./data/person_property.json').then((res) => {
					res.json().then((data) => {
						this.personInfo = data;
						resolve();
					});
				});
			} else {
				window.Channel.loadJSON().then((data) => {
					this.personInfo = data;
					resolve();
				});
			}
		});
	}

	createCharacters() {
		return new Promise(async (resolve, reject) => {
			for (const model of models) {
				await this.loadModel(model);
				for (const texture of model.texture) {
					await this.loadTexture(texture);
				}
			}
			resolve();
		});
	}
	async loadMoveData() {
		// 加载移动数据（在ViewEngine中已经设置为第5步的一部分，从60%开始）
		try {
			let data = [];
			if (import.meta.env.DEV) {
				const response = await fetch('./data/output.json');
				globalLoading.updateStepProgress(70);
				data = await response.json();
				globalLoading.updateStepProgress(80);
			} else {
				data = await window.Channel.loadMoveData((progress) => {
					// 将0-100的进度映射到60-80的范围
					const mappedProgress = 60 + progress * 0.2;
					globalLoading.updateStepProgress(mappedProgress);
				});
			}
			// 创建角色克隆
			const charactersCount = this.personInfo.length;

			Array.from({ length: charactersCount }).forEach((_, index) => {
				const sex = this.personInfo[index].sex;
				const age = this.personInfo[index].age;

				// 根据性别和年龄判断模型类型
				let modelType;
				if (age < 18) {
					// modelType = sex === 'M' ? 'childMale' : 'childFemale';
					modelType = sex === 'M' ? 'childMale' : 'childMale';
				} else if (age >= 60) {
					modelType = sex === 'M' ? 'elderMale' : 'elderFemale';
				} else {
					modelType = sex === 'M' ? 'adultMale' : 'adultFemale';
				}

				// modelType = 'childFemale';
				const model = getRandomModelByType(modelType);

				const cloneModel = SkeletonUtils.clone(this.models.get(model.model));

				// cloneModel.position.set(10 + index * 0.5, 10, 10);
				const min = 1.3;
				const max = 1.8;
				const randomHeight = min + Math.random() * (max - min);
				const scale = randomHeight / 1.7;
				cloneModel.scale.multiplyScalar(scale);

				this.applyTextureToModel(cloneModel, this.textures.get(model.texture));

				const mixer = new THREE.AnimationMixer(cloneModel);

				// 使用对应模型的animations
				const modelAnimations = this.modelAnimations.get(model.model);
				const actions = {};
				modelAnimations.forEach((anim, animIndex) => {
					const action = mixer.clipAction(anim);
					actions[animIndex] = action;
				});
				this.animationActions[index] = actions;
				this.animationMixers.push(mixer);

				// 添加对象信息用于识别
				cloneModel.userData = {
					type: 'character',
					personData: this.personInfo[index],
					_id: v4(),
				};

				// 添加到可点击对象列表
				this.viewEngine.clickableObjects.push(cloneModel);

				this.characters.push(cloneModel);
				this.viewEngine.scene.add(cloneModel);

				// 更新克隆进度
				const cloneProgress = 80 + ((index + 1) / charactersCount) * 15;
				globalLoading.updateStepProgress(cloneProgress);
			});

			this.moveData = data;
			// 不更新进度，让ViewEngine统一管理

			// 通知ViewEngine更新状态显示
			if (this.viewEngine.updatePlaybackStatus) {
				this.viewEngine.updatePlaybackStatus();
			}

			// 不需要调用nextStep，因为这是ViewEngine第5步的一部分
			return data;
		} catch (error) {
			console.error('移动数据加载失败:', error);
			throw error;
		}
	}
	async load() {
		await this.createCharacters();
		await this.loadMoveData();
	}

	normalizeAngleDiff(angleDiff) {
		return Math.atan2(Math.sin(angleDiff), Math.cos(angleDiff));
	}

	update(delta) {
		if (!this.isPlaying) {
			this.animationMixers.forEach((mixer) => mixer.update(delta));
			return;
		}

		const currentTime = Date.now();
		const moveData = this.moveData;

		this.animationMixers.forEach((mixer) => mixer.update(delta));

		if (moveData.length === 0 || this.timeIndex >= moveData.length) {
			// 当没有移动数据或已到达数据末尾时，停止所有动画
			this.characters.forEach((_, index) => {
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
			});
			return;
		}

		const shouldUpdateFrame =
			currentTime - this.lastUpdateTime >= this.updateInterval;

		if (shouldUpdateFrame && moveData[this.timeIndex]) {
			this.updateTargetData(moveData[this.timeIndex]);
			this.lastUpdateTime = currentTime;
			this.timeIndex++;
		}

		// 修复：使用当前timeIndex减1来获取当前帧数据，因为timeIndex在updateTargetData后已经+1了
		const currentFrameIndex = Math.max(0, this.timeIndex - 1);
		if (moveData[currentFrameIndex]) {
			this.updateCharacters(moveData[currentFrameIndex], delta);
		}
	}

	updateTargetData(frameData) {
		const items = Object.values(frameData)[0];
		const bounds = this.viewEngine.bounds;

		this.characters.forEach((character, index) => {
			const newPos = items[index];
			const position = new THREE.Vector3(
				newPos.x - bounds.centerX,
				newPos.z - bounds.centerZ,
				newPos.y - bounds.centerY
			);

			this.initializeCharacterData(index, position);

			// 只使用移动方向计算旋转
			const moveVector = position.clone().sub(this.previousPositions[index]);
			if (moveVector.length() > 0.001) {
				this.targetRotations[index] = Math.atan2(moveVector.x, moveVector.z);
			}

			this.targetPositions[index].copy(position);
			this.previousPositions[index].copy(position);
		});
	}

	initializeCharacterData(index, position) {
		if (!this.previousPositions[index]) {
			this.previousPositions[index] = position.clone();
		}
		if (!this.targetPositions[index]) {
			this.targetPositions[index] = new THREE.Vector3();
		}
		if (this.targetRotations[index] === undefined) {
			this.targetRotations[index] = 0;
		}
	}

	updateCharacters(frameData, delta) {
		const items = Object.values(frameData)[0];
		// 修复：当timeIndex为1时表示正在处理第0帧（因为已经+1了），这应该是第一帧
		const isFirstFrame = this.timeIndex <= 1;

		this.characters.forEach((character, index) => {
			// 如果没有对应的移动数据，停止该角色的所有动画
			if (!items[index] || !this.targetPositions[index]) {
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
				return;
			}

			this.updateCharacterRotation(character, index, isFirstFrame, delta);

			this.updateCharacterPosition(character, index, isFirstFrame);

			this.updateCharacterAnimation(index, items[index].p);
		});
	}

	updateCharacterPosition(character, index, isFirstFrame) {
		if (isFirstFrame) {
			character.position.copy(this.targetPositions[index]);
		} else {
			character.position.lerp(this.targetPositions[index], this.lerpFactor);
		}
	}

	updateCharacterRotation(character, index, isFirstFrame, delta) {
		if (this.targetRotations[index] === undefined) return;

		if (isFirstFrame) {
			character.rotation.y = this.targetRotations[index];
		} else {
			const angleDiff = this.normalizeAngleDiff(
				this.targetRotations[index] - character.rotation.y
			);
			const rotationSpeed = 3.0;
			const maxRotation = rotationSpeed * delta;
			const clampedAngleDiff =
				Math.sign(angleDiff) * Math.min(Math.abs(angleDiff), maxRotation);
			character.rotation.y += clampedAngleDiff;
		}
	}

	updateCharacterAnimation(index, statusCode) {
		const statusMap = {
			1: 1,
			2: 2,
			3: 0,
			4: 3,
			5: 5,
			6: 0,
			7: 4,
			8: 6,
			9: 0,
		};
		// p: 1 平地正常  2 平地小步伐 3 平地静止 4 上楼梯正常 5 上楼梯小步伐  6 上楼梯静止 7 下楼梯正常 8 下楼梯小步伐  9 下楼梯静止

		const statusIndex = statusMap[statusCode];

		if (statusIndex === 0) {
			if (this.characterStates[index]) {
				this.characterStates[index].paused = true;
			}
		} else {
			const action = this.animationActions[index][statusIndex];

			if (!this.characterStates[index]) {
				this.characterStates[index] = action;
				action.play();
				return;
			}

			if (
				this.characterStates[index] === action &&
				this.characterStates[index].paused
			) {
				this.characterStates[index].paused = false;
				this.characterStates[index].play();
			} else if (this.characterStates[index] !== action) {
				if (this.characterStates[index]) {
					this.characterStates[index].fadeOut(0.2);
				}
				this.characterStates[index] = action;
				action.reset().fadeIn(0.2).play();
			} else if (
				this.characterStates[index] === action &&
				!this.characterStates[index].paused
			) {
				if (!action.isRunning()) {
					action.play();
				}
			}
		}
	}

	play() {
		this.isPlaying = true;
		// 重置时间戳，确保不会跳帧
		this.lastUpdateTime = Date.now();

		// 如果timeIndex为0，立即处理第一帧
		if (this.timeIndex === 0 && this.moveData.length > 0) {
			this.updateTargetData(this.moveData[0]);
			this.timeIndex = 1;
		}
	}

	pause() {
		this.isPlaying = false;
	}

	reset() {
		this.isPlaying = false;
		this.timeIndex = 0;
		this.lastUpdateTime = 0;

		// 重置所有角色状态
		this.targetPositions = [];
		this.targetRotations = [];
		this.previousPositions = [];
		this.characterStates = [];

		// 重置角色位置到初始状态
		this.characters.forEach((character, index) => {
			if (this.moveData.length > 0 && this.moveData[0]) {
				const items = Object.values(this.moveData[0])[0];
				const bounds = this.viewEngine.bounds;
				const newPos = items[index];
				const position = new THREE.Vector3(
					newPos.x - bounds.centerX,
					newPos.z - bounds.centerZ,
					newPos.y - bounds.centerY
				);
				character.position.copy(position);

				// 重置旋转为默认方向（朝向Z轴负方向）
				character.rotation.y = 0;

				// 停止所有动画
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
			}
		});
	}

	seekToFrame(frameIndex, isDragging = false) {
		// 确保frameIndex在有效范围内
		frameIndex = Math.max(0, Math.min(frameIndex, this.moveData.length - 1));

		// 暂停播放，防止在拖动时继续自动播放
		const wasPlaying = this.isPlaying;
		this.isPlaying = false;

		this.timeIndex = frameIndex;
		this.lastUpdateTime = Date.now();

		// 如果有数据，立即更新到指定帧
		if (this.moveData.length > 0 && this.moveData[frameIndex]) {
			// 清空之前的状态
			this.targetPositions = [];
			this.targetRotations = [];
			this.previousPositions = [];

			const items = Object.values(this.moveData[frameIndex])[0];
			const bounds = this.viewEngine.bounds;

			// 立即应用位置和旋转，不使用lerp
			this.characters.forEach((character, index) => {
				const newPos = items[index];

				// 设置位置
				const position = new THREE.Vector3(
					newPos.x - bounds.centerX,
					newPos.z - bounds.centerZ,
					newPos.y - bounds.centerY
				);
				character.position.copy(position);

				// 计算旋转 - 通过与前一帧比较来确定方向
				let rotation = character.rotation.y; // 保持当前旋转作为默认值

				if (frameIndex > 0 && this.moveData[frameIndex - 1]) {
					const prevItems = Object.values(this.moveData[frameIndex - 1])[0];
					const prevPos = prevItems[index];
					const prevPosition = new THREE.Vector3(
						prevPos.x - bounds.centerX,
						prevPos.z - bounds.centerZ,
						prevPos.y - bounds.centerY
					);

					const moveVector = position.clone().sub(prevPosition);
					if (moveVector.length() > 0.001) {
						rotation = Math.atan2(moveVector.x, moveVector.z);
					}
				}

				character.rotation.y = rotation;

				// 更新内部状态
				this.targetPositions[index] = position.clone();
				this.targetRotations[index] = rotation;
				this.previousPositions[index] = position.clone();

				// 更新动画状态
				this.updateCharacterAnimation(index, newPos.p);
			});
		}

		// 优化播放状态恢复逻辑
		if (wasPlaying && !isDragging) {
			// 如果不是拖拽操作，立即恢复播放状态，减少延迟
			this.isPlaying = true;
			this.lastUpdateTime = Date.now();
		} else if (wasPlaying && isDragging) {
			// 如果是拖拽操作，使用更短的延迟
			setTimeout(() => {
				if (!this.viewEngine.isDragging) {
					this.isPlaying = true;
					this.lastUpdateTime = Date.now();
				}
			}, 50); // 减少延迟从150ms到50ms
		}
	}

	// 添加获取时间的方法
	getTimeInfo() {
		const currentFrameIndex = this.timeIndex;
		const totalFrames = this.moveData.length;

		// 计算当前时间（秒）
		const currentTimeSeconds = currentFrameIndex / this.frameRate;
		// 计算总时间（秒）
		const totalTimeSeconds = totalFrames / this.frameRate;

		// 格式化为 分:秒 格式
		const formatTime = (seconds) => {
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = Math.floor(seconds % 60);
			return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
		};

		return {
			currentTime: formatTime(currentTimeSeconds),
			totalTime: formatTime(totalTimeSeconds),
			currentTimeSeconds,
			totalTimeSeconds,
		};
	}

	// 设置人物材质颜色
	setCharacterColor(color) {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				material.color.set(color);
			}
		});
	}

	// 设置人物材质透明度
	setCharacterOpacity(opacity) {
		this.characterMaterials.forEach((material) => {
			if (material) {
				material.transparent = opacity < 1.0;
				material.opacity = opacity;
			}
		});
	}

	// 重置人物材质为默认设置
	resetCharacterMaterial() {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				material.color.set(0xffffff); // 白色
				material.transparent = false;
				material.opacity = 1.0;
			}
		});
	}

	// 应用更自然的人物材质设置
	applyNaturalCharacterMaterial() {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				// 稍微调暗，更自然
				material.color.set(0xf0f0f0);
				material.transparent = false;
				material.opacity = 1.0;
			}
		});
	}
}
